#!/usr/bin/env python3
"""
MySQL连接诊断脚本
"""
import pymysql
import sys


def test_mysql_connection():
    """测试MySQL连接"""
    print("MySQL连接诊断")
    print("=" * 40)
    
    # 测试配置
    configs = [
        {
            'name': '直接连接localhost',
            'host': 'localhost',
            'port': 3306,
            'user': 'agent',
            'password': '123456',
            'database': 'trade-ai'
        },
        {
            'name': '连接127.0.0.1',
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'agent',
            'password': '123456',
            'database': 'trade-ai'
        },
        {
            'name': '不指定数据库',
            'host': 'localhost',
            'port': 3306,
            'user': 'agent',
            'password': '123456'
        }
    ]
    
    for config in configs:
        print(f"\n测试: {config['name']}")
        try:
            # 移除name字段
            conn_config = {k: v for k, v in config.items() if k != 'name'}
            
            connection = pymysql.connect(**conn_config)
            print("✅ 连接成功!")
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                print(f"   MySQL版本: {version[0]}")
                
                cursor.execute("SELECT DATABASE()")
                db = cursor.fetchone()
                print(f"   当前数据库: {db[0] if db[0] else 'None'}")
                
                if 'database' not in conn_config:
                    cursor.execute("SHOW DATABASES")
                    databases = cursor.fetchall()
                    print(f"   可用数据库: {[db[0] for db in databases]}")
            
            connection.close()
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
    
    return False


if __name__ == "__main__":
    success = test_mysql_connection()
    if success:
        print("\n🎉 至少有一个配置可以连接!")
    else:
        print("\n❌ 所有配置都失败了")
    sys.exit(0 if success else 1)
