#!/usr/bin/env python3
"""
简单的MySQL连接测试脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data.access.mysql_access import MySQLDataAccess


def test_mysql_simple():
    """简单的MySQL连接测试"""
    print("MySQL连接测试")
    print("=" * 40)
    
    # 测试不同的连接参数
    test_configs = [
        {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'database': 'test_stock_selection'
        },
        {
            'host': '127.0.0.1',
            'user': 'root', 
            'password': '',
            'database': 'test_stock_selection'
        }
    ]
    
    for i, config in enumerate(test_configs):
        print(f"\n测试配置 {i+1}: {config['host']}:{config.get('port', 3306)}")
        try:
            mysql_access = MySQLDataAccess(**config)
            print("✅ 连接成功！")
            print("✅ 数据库初始化成功！")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
    
    print("\n所有配置都失败了。请检查:")
    print("1. MySQL服务器是否运行")
    print("2. 用户权限是否正确")
    print("3. 防火墙设置")
    return False


if __name__ == "__main__":
    test_mysql_simple()
