#!/usr/bin/env python3
"""
MySQL数据库测试脚本
测试MySQL数据访问功能
"""
import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data.sources.akshare_source import AkshareDataSource
from src.data.access.mysql_access import MySQLDataAccess
from src.data.access.data_access_factory import DataAccessFactory


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/test_mysql.log')
        ]
    )


def test_mysql_connection():
    """测试MySQL连接"""
    print("=" * 50)
    print("测试MySQL连接")
    print("=" * 50)
    
    try:
        # 使用工厂创建MySQL访问实例
        data_access = DataAccessFactory.create_mysql_access()
        print("✅ MySQL连接成功")
        print("✅ 数据库初始化成功")
        return data_access
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {str(e)}")
        print("\n请确保:")
        print("1. MySQL服务器正在运行")
        print("2. 连接参数正确（host=localhost, user=root等）")
        print("3. 用户有创建数据库的权限")
        return None


def test_data_operations(data_access):
    """测试数据操作"""
    print("\n" + "=" * 50)
    print("测试数据操作")
    print("=" * 50)
    
    try:
        # 测试保存股票信息
        print("1. 测试保存股票信息:")
        test_stock_info = {
            'stock_code': '000001',
            'stock_name': '平安银行',
            'industry': '银行',
            'market': '深交所',
            'list_date': datetime(1991, 4, 3).date()
        }
        
        success = data_access.save_stock_info(test_stock_info)
        print(f"   保存单个股票信息: {'成功' if success else '失败'}")
        
        # 测试批量保存
        test_stock_list = [
            {
                'stock_code': '000002',
                'stock_name': '万科A',
                'industry': '房地产',
                'market': '深交所',
                'list_date': datetime(1991, 1, 29).date()
            },
            {
                'stock_code': '600000',
                'stock_name': '浦发银行',
                'industry': '银行',
                'market': '上交所',
                'list_date': datetime(1999, 11, 10).date()
            }
        ]
        
        success = data_access.save_stock_info_batch(test_stock_list)
        print(f"   批量保存股票信息: {'成功' if success else '失败'}")
        
        # 测试查询股票信息
        print("\n2. 测试查询股票信息:")
        stock_info = data_access.get_stock_info('000001')
        if stock_info:
            print(f"   查询结果: {stock_info['stock_code']} - {stock_info['stock_name']}")
        
        all_codes = data_access.get_all_stock_codes()
        print(f"   数据库中股票总数: {len(all_codes)}")
        
        # 测试保存交易数据
        print("\n3. 测试保存交易数据:")
        test_daily_data = [
            {
                'stock_code': '000001',
                'trade_date': datetime.now().date(),
                'open_price': 10.50,
                'close_price': 10.80,
                'high_price': 11.00,
                'low_price': 10.30,
                'volume': 1000000,
                'amount': 10800000.0,
                'turnover_rate': 0.5
            }
        ]
        
        success = data_access.save_daily_data(test_daily_data)
        print(f"   保存交易数据: {'成功' if success else '失败'}")
        
        # 测试查询交易数据
        print("\n4. 测试查询交易数据:")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        trading_data = data_access.get_stock_data('000001', start_date, end_date)
        print(f"   查询最近7天交易数据: {len(trading_data)} 条记录")
        
        latest_date = data_access.get_latest_trade_date('000001')
        if latest_date:
            print(f"   最新交易日期: {latest_date.strftime('%Y-%m-%d')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据操作测试失败: {str(e)}")
        return False


def test_with_real_data():
    """使用真实数据测试"""
    print("\n" + "=" * 50)
    print("使用真实数据测试")
    print("=" * 50)
    
    try:
        # 创建数据源和数据访问实例
        data_source = AkshareDataSource()
        data_access = DataAccessFactory.create_mysql_access()
        
        print("1. 获取少量股票数据进行测试...")
        stock_list = data_source.get_stock_list()
        
        # 只取前5只股票进行测试
        test_stocks = stock_list[:5]
        success = data_access.save_stock_info_batch(test_stocks)
        print(f"   保存 {len(test_stocks)} 只股票信息: {'成功' if success else '失败'}")
        
        print("\n2. 获取交易数据...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=5)
        
        success_count = 0
        for stock in test_stocks[:2]:  # 只测试前2只
            try:
                stock_code = stock['stock_code']
                daily_data = data_source.get_daily_data(stock_code, start_date, end_date)
                
                if daily_data:
                    data_access.save_daily_data(daily_data)
                    success_count += 1
                    print(f"   {stock_code}: {len(daily_data)} 条记录")
                
            except Exception as e:
                print(f"   {stock_code}: 失败 - {str(e)}")
        
        print(f"\n3. 真实数据测试完成，成功处理 {success_count} 只股票")
        return True
        
    except Exception as e:
        print(f"❌ 真实数据测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("MySQL数据库功能测试")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    try:
        # 测试MySQL连接
        data_access = test_mysql_connection()
        if not data_access:
            return False
        
        # 测试数据操作
        data_ops_success = test_data_operations(data_access)
        
        # 使用真实数据测试
        real_data_success = test_with_real_data()
        
        # 总结
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        print(f"MySQL连接测试: {'通过' if data_access else '失败'}")
        print(f"数据操作测试: {'通过' if data_ops_success else '失败'}")
        print(f"真实数据测试: {'通过' if real_data_success else '失败'}")
        
        if data_access and data_ops_success and real_data_success:
            print("\n🎉 MySQL数据库测试全部通过！")
            print("✅ MySQL连接正常")
            print("✅ 数据库表创建成功")
            print("✅ 数据CRUD操作正常")
            print("✅ 与akshare集成正常")
            return True
        else:
            print("\n❌ 部分测试失败，请检查错误信息")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        return False


if __name__ == "__main__":
    main()
