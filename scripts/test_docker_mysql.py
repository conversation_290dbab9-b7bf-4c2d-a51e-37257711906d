#!/usr/bin/env python3
"""
Docker MySQL数据库测试脚本
专门测试Docker部署的MySQL（用户名：agent，密码：123456，数据库：trade-ai）
"""
import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data.sources.akshare_source import AkshareDataSource
from src.data.access.mysql_access import MySQLDataAccess


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/test_docker_mysql.log')
        ]
    )


def test_docker_mysql_connection():
    """测试Docker MySQL连接"""
    print("=" * 50)
    print("测试Docker MySQL连接")
    print("=" * 50)
    
    try:
        # 使用Docker MySQL配置
        mysql_access = MySQLDataAccess(
            host='localhost',
            port=3306,
            user='agent',
            password='123456',
            database='trade-ai'
        )
        print("✅ Docker MySQL连接成功")
        print("✅ 数据库表初始化成功")
        return mysql_access
        
    except Exception as e:
        print(f"❌ Docker MySQL连接失败: {str(e)}")
        print("\n请确保:")
        print("1. Docker MySQL容器正在运行")
        print("2. 端口3306已映射到主机")
        print("3. 用户agent有访问trade-ai数据库的权限")
        return None


def test_basic_operations(mysql_access):
    """测试基本数据库操作"""
    print("\n" + "=" * 50)
    print("测试基本数据库操作")
    print("=" * 50)
    
    try:
        # 测试保存股票信息
        print("1. 测试保存股票信息:")
        test_stock_info = {
            'stock_code': '000001',
            'stock_name': '平安银行',
            'industry': '银行',
            'market': '深交所',
            'list_date': datetime(1991, 4, 3).date()
        }
        
        success = mysql_access.save_stock_info(test_stock_info)
        print(f"   保存单个股票信息: {'成功' if success else '失败'}")
        
        # 测试查询股票信息
        print("\n2. 测试查询股票信息:")
        stock_info = mysql_access.get_stock_info('000001')
        if stock_info:
            print(f"   查询结果: {stock_info['stock_code']} - {stock_info['stock_name']}")
        else:
            print("   查询结果: 未找到数据")
        
        # 测试保存交易数据
        print("\n3. 测试保存交易数据:")
        test_daily_data = [
            {
                'stock_code': '000001',
                'trade_date': datetime.now().date(),
                'open_price': 10.50,
                'close_price': 10.80,
                'high_price': 11.00,
                'low_price': 10.30,
                'volume': 1000000,
                'amount': 10800000.0,
                'turnover_rate': 0.5
            }
        ]
        
        success = mysql_access.save_daily_data(test_daily_data)
        print(f"   保存交易数据: {'成功' if success else '失败'}")
        
        # 测试查询交易数据
        print("\n4. 测试查询交易数据:")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=1)
        trading_data = mysql_access.get_stock_data('000001', start_date, end_date)
        print(f"   查询最近1天交易数据: {len(trading_data)} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本操作测试失败: {str(e)}")
        return False


def test_with_akshare_data(mysql_access):
    """使用akshare真实数据测试"""
    print("\n" + "=" * 50)
    print("使用akshare真实数据测试")
    print("=" * 50)
    
    try:
        # 创建数据源实例
        data_source = AkshareDataSource()
        
        print("1. 获取少量股票数据进行测试...")
        stock_list = data_source.get_stock_list()
        
        # 只取前3只股票进行测试
        test_stocks = stock_list[:3]
        success = mysql_access.save_stock_info_batch(test_stocks)
        print(f"   保存 {len(test_stocks)} 只股票信息: {'成功' if success else '失败'}")
        
        print("\n2. 获取交易数据...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3)
        
        success_count = 0
        for stock in test_stocks[:2]:  # 只测试前2只
            try:
                stock_code = stock['stock_code']
                daily_data = data_source.get_daily_data(stock_code, start_date, end_date)
                
                if daily_data:
                    mysql_access.save_daily_data(daily_data)
                    success_count += 1
                    print(f"   {stock_code}: {len(daily_data)} 条记录")
                
            except Exception as e:
                print(f"   {stock_code}: 失败 - {str(e)}")
        
        print(f"\n3. akshare数据测试完成，成功处理 {success_count} 只股票")
        
        # 显示统计信息
        all_codes = mysql_access.get_all_stock_codes()
        print(f"   数据库中总股票数: {len(all_codes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ akshare数据测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("Docker MySQL数据库功能测试")
    print("配置: host=localhost, user=agent, password=123456, database=trade-ai")
    print("=" * 70)
    
    # 设置日志
    setup_logging()
    
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    try:
        # 测试MySQL连接
        mysql_access = test_docker_mysql_connection()
        if not mysql_access:
            return False
        
        # 测试基本操作
        basic_ops_success = test_basic_operations(mysql_access)
        
        # 使用真实数据测试
        akshare_success = test_with_akshare_data(mysql_access)
        
        # 总结
        print("\n" + "=" * 70)
        print("测试总结")
        print("=" * 70)
        print(f"Docker MySQL连接测试: {'通过' if mysql_access else '失败'}")
        print(f"基本操作测试: {'通过' if basic_ops_success else '失败'}")
        print(f"akshare集成测试: {'通过' if akshare_success else '失败'}")
        
        if mysql_access and basic_ops_success and akshare_success:
            print("\n🎉 Docker MySQL数据库测试全部通过！")
            print("✅ Docker MySQL连接正常")
            print("✅ 数据库表创建成功")
            print("✅ 数据CRUD操作正常")
            print("✅ 与akshare集成正常")
            print("\n现在可以使用MySQL作为主要数据库了！")
            return True
        else:
            print("\n❌ 部分测试失败，请检查错误信息")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        return False


if __name__ == "__main__":
    main()
