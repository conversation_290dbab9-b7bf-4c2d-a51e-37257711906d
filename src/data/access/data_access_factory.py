"""
数据访问工厂类
"""
import os
from typing import Optional

from ...core.interfaces.data_access import IDataAccess
from .mysql_access import MySQLDataAccess
from .sqlite_access import SQLiteDataAccess
from ...config.database_config import db_config


class DataAccessFactory:
    """数据访问工厂类"""
    
    @staticmethod
    def create_data_access(db_type: Optional[str] = None) -> IDataAccess:
        """
        创建数据访问实例
        
        Args:
            db_type: 数据库类型 ('mysql' 或 'sqlite')，如果为None则从环境变量读取
            
        Returns:
            IDataAccess: 数据访问实例
        """
        if db_type is None:
            db_type = os.getenv('DATABASE_TYPE', 'mysql').lower()
        
        if db_type == 'mysql':
            mysql_config = db_config.get_mysql_config()
            return MySQLDataAccess(**mysql_config)
        elif db_type == 'sqlite':
            sqlite_config = db_config.get_sqlite_config()
            return SQLiteDataAccess(sqlite_config['db_path'])
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
    
    @staticmethod
    def create_mysql_access() -> MySQLDataAccess:
        """创建MySQL数据访问实例"""
        mysql_config = db_config.get_mysql_config()
        return MySQLDataAccess(**mysql_config)
    
    @staticmethod
    def create_sqlite_access() -> SQLiteDataAccess:
        """创建SQLite数据访问实例"""
        sqlite_config = db_config.get_sqlite_config()
        return SQLiteDataAccess(sqlite_config['db_path'])
