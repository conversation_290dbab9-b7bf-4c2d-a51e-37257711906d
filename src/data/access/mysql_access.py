"""
MySQL数据访问实现
"""
import pymysql
import logging
from typing import List, Dict, Optional
from datetime import datetime
import os
from contextlib import contextmanager

from ...core.interfaces.data_access import IDataAccess
from ...core.exceptions.custom_exceptions import DataAccessError


class MySQLDataAccess(IDataAccess):
    """MySQL数据访问实现"""

    def __init__(self, host: str = "localhost", port: int = 3306,
                 user: str = "agent", password: str = "123456",
                 database: str = "trade-ai", charset: str = "utf8mb4"):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.charset = charset
        self.logger = logging.getLogger(__name__)

        # 初始化数据库表（假设数据库已存在）
        self.init_database()

    @contextmanager
    def _get_connection(self):
        """获取数据库连接（上下文管理器）"""
        connection = None
        try:
            connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset=self.charset,
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=False,
                connect_timeout=10,
                read_timeout=10,
                write_timeout=10
            )
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            raise DataAccessError(f"数据库连接失败: {str(e)}")
        finally:
            if connection:
                connection.close()

    def _create_database_if_not_exists(self):
        """创建数据库（如果不存在）"""
        try:
            # 连接到MySQL服务器（不指定数据库）
            connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                charset=self.charset
            )

            with connection.cursor() as cursor:
                cursor.execute(f"CREATE DATABASE IF NOT EXISTS {self.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                connection.commit()
                self.logger.info(f"数据库 {self.database} 创建成功或已存在")

            connection.close()
        except Exception as e:
            raise DataAccessError(f"创建数据库失败: {str(e)}")

    def init_database(self) -> bool:
        """初始化数据库表"""
        try:
            # 直接创建表（假设数据库已存在）
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    # 创建股票基本信息表
                    cursor.execute('''
                        CREATE TABLE IF NOT EXISTS stock_info (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            stock_code VARCHAR(10) NOT NULL UNIQUE,
                            stock_name VARCHAR(50) NOT NULL,
                            industry VARCHAR(50),
                            market VARCHAR(10),
                            list_date DATE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_stock_code (stock_code)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ''')

                    # 创建日交易数据表
                    cursor.execute('''
                        CREATE TABLE IF NOT EXISTS daily_trading (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            stock_code VARCHAR(10) NOT NULL,
                            trade_date DATE NOT NULL,
                            open_price DECIMAL(10,2),
                            close_price DECIMAL(10,2),
                            high_price DECIMAL(10,2),
                            low_price DECIMAL(10,2),
                            volume BIGINT,
                            amount DECIMAL(15,2),
                            turnover_rate DECIMAL(8,4),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            UNIQUE KEY uk_stock_date (stock_code, trade_date),
                            INDEX idx_stock_code (stock_code),
                            INDEX idx_trade_date (trade_date)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ''')

                    # 创建选股结果表
                    cursor.execute('''
                        CREATE TABLE IF NOT EXISTS selection_results (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            strategy_name VARCHAR(50) NOT NULL,
                            stock_code VARCHAR(10) NOT NULL,
                            selection_date DATE NOT NULL,
                            score DECIMAL(8,4),
                            reason TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            INDEX idx_strategy_date (strategy_name, selection_date),
                            INDEX idx_stock_code (stock_code)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ''')

                    # 创建系统配置表
                    cursor.execute('''
                        CREATE TABLE IF NOT EXISTS system_config (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            config_key VARCHAR(100) NOT NULL UNIQUE,
                            config_value TEXT,
                            description TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ''')

                conn.commit()
                self.logger.info("MySQL数据库初始化成功")
                return True

        except Exception as e:
            error_msg = f"MySQL数据库初始化失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def save_stock_info(self, stock_info: Dict) -> bool:
        """保存股票基本信息"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute('''
                        INSERT INTO stock_info
                        (stock_code, stock_name, industry, market, list_date, updated_at)
                        VALUES (%(stock_code)s, %(stock_name)s, %(industry)s, %(market)s, %(list_date)s, %(updated_at)s)
                        ON DUPLICATE KEY UPDATE
                        stock_name = VALUES(stock_name),
                        industry = VALUES(industry),
                        market = VALUES(market),
                        list_date = VALUES(list_date),
                        updated_at = VALUES(updated_at)
                    ''', {
                        'stock_code': stock_info['stock_code'],
                        'stock_name': stock_info['stock_name'],
                        'industry': stock_info.get('industry', ''),
                        'market': stock_info.get('market', ''),
                        'list_date': stock_info.get('list_date'),
                        'updated_at': datetime.now()
                    })

                conn.commit()
                return True

        except Exception as e:
            error_msg = f"保存股票信息失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def save_stock_info_batch(self, stock_info_list: List[Dict]) -> bool:
        """批量保存股票基本信息"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    data_list = []
                    for stock_info in stock_info_list:
                        data_list.append({
                            'stock_code': stock_info['stock_code'],
                            'stock_name': stock_info['stock_name'],
                            'industry': stock_info.get('industry', ''),
                            'market': stock_info.get('market', ''),
                            'list_date': stock_info.get('list_date'),
                            'updated_at': datetime.now()
                        })

                    if data_list:
                        cursor.executemany('''
                            INSERT INTO stock_info
                            (stock_code, stock_name, industry, market, list_date, updated_at)
                            VALUES (%(stock_code)s, %(stock_name)s, %(industry)s, %(market)s, %(list_date)s, %(updated_at)s)
                            ON DUPLICATE KEY UPDATE
                            stock_name = VALUES(stock_name),
                            industry = VALUES(industry),
                            market = VALUES(market),
                            list_date = VALUES(list_date),
                            updated_at = VALUES(updated_at)
                        ''', data_list)

                conn.commit()
                self.logger.info(f"批量保存股票信息成功，共 {len(data_list)} 条")
                return True

        except Exception as e:
            error_msg = f"批量保存股票信息失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def get_stock_info(self, stock_code: str) -> Optional[Dict]:
        """获取股票基本信息"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute('''
                        SELECT * FROM stock_info WHERE stock_code = %s
                    ''', (stock_code,))

                    result = cursor.fetchone()
                    return dict(result) if result else None

        except Exception as e:
            error_msg = f"获取股票信息失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def get_all_stock_codes(self) -> List[str]:
        """获取所有股票代码"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute('SELECT stock_code FROM stock_info ORDER BY stock_code')
                    results = cursor.fetchall()
                    return [row['stock_code'] for row in results]

        except Exception as e:
            error_msg = f"获取股票代码列表失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def save_daily_data(self, daily_data: List[Dict]) -> bool:
        """保存日交易数据"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    data_list = []
                    for data in daily_data:
                        # 确保日期格式正确
                        trade_date = data['trade_date']
                        if hasattr(trade_date, 'date'):
                            trade_date = trade_date.date()
                        elif isinstance(trade_date, str):
                            trade_date = datetime.strptime(trade_date, '%Y-%m-%d').date()

                        data_list.append({
                            'stock_code': data['stock_code'],
                            'trade_date': trade_date,
                            'open_price': data['open_price'],
                            'close_price': data['close_price'],
                            'high_price': data['high_price'],
                            'low_price': data['low_price'],
                            'volume': data['volume'],
                            'amount': data['amount'],
                            'turnover_rate': data.get('turnover_rate'),
                            'created_at': datetime.now()
                        })

                    if data_list:
                        cursor.executemany('''
                            INSERT INTO daily_trading
                            (stock_code, trade_date, open_price, close_price, high_price,
                             low_price, volume, amount, turnover_rate, created_at)
                            VALUES (%(stock_code)s, %(trade_date)s, %(open_price)s, %(close_price)s,
                                   %(high_price)s, %(low_price)s, %(volume)s, %(amount)s,
                                   %(turnover_rate)s, %(created_at)s)
                            ON DUPLICATE KEY UPDATE
                            open_price = VALUES(open_price),
                            close_price = VALUES(close_price),
                            high_price = VALUES(high_price),
                            low_price = VALUES(low_price),
                            volume = VALUES(volume),
                            amount = VALUES(amount),
                            turnover_rate = VALUES(turnover_rate)
                        ''', data_list)

                conn.commit()
                self.logger.info(f"保存日交易数据成功，共 {len(data_list)} 条")
                return True

        except Exception as e:
            error_msg = f"保存日交易数据失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def get_stock_data(self, stock_code: str, start_date: datetime, end_date: datetime) -> List[Dict]:
        """查询股票交易数据"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute('''
                        SELECT * FROM daily_trading
                        WHERE stock_code = %s AND trade_date BETWEEN %s AND %s
                        ORDER BY trade_date
                    ''', (stock_code, start_date.date(), end_date.date()))

                    results = cursor.fetchall()
                    return [dict(row) for row in results]

        except Exception as e:
            error_msg = f"查询股票交易数据失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def get_latest_trade_date(self, stock_code: str) -> Optional[datetime]:
        """获取股票最新交易日期"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute('''
                        SELECT MAX(trade_date) as latest_date
                        FROM daily_trading
                        WHERE stock_code = %s
                    ''', (stock_code,))

                    result = cursor.fetchone()
                    if result and result['latest_date']:
                        return datetime.combine(result['latest_date'], datetime.min.time())
                    return None

        except Exception as e:
            error_msg = f"获取最新交易日期失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def save_selection_result(self, selection_result: Dict) -> bool:
        """保存选股结果"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute('''
                        INSERT INTO selection_results
                        (strategy_name, stock_code, selection_date, score, reason)
                        VALUES (%(strategy_name)s, %(stock_code)s, %(selection_date)s, %(score)s, %(reason)s)
                    ''', {
                        'strategy_name': selection_result['strategy_name'],
                        'stock_code': selection_result['stock_code'],
                        'selection_date': selection_result['selection_date'],
                        'score': selection_result['score'],
                        'reason': selection_result['reason']
                    })

                conn.commit()
                return True

        except Exception as e:
            error_msg = f"保存选股结果失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def get_selection_results(self, strategy_name: str, selection_date: datetime) -> List[Dict]:
        """获取选股结果"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute('''
                        SELECT sr.*, si.stock_name
                        FROM selection_results sr
                        LEFT JOIN stock_info si ON sr.stock_code = si.stock_code
                        WHERE sr.strategy_name = %s AND sr.selection_date = %s
                        ORDER BY sr.score DESC
                    ''', (strategy_name, selection_date.date()))

                    results = cursor.fetchall()
                    return [dict(row) for row in results]

        except Exception as e:
            error_msg = f"获取选股结果失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def close_connection(self) -> None:
        """关闭数据库连接"""
        # MySQL连接通过上下文管理器自动管理，这里不需要特殊处理
        pass
