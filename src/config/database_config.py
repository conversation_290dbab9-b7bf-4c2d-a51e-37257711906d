"""
数据库配置管理
"""
import os
from typing import Dict, Any


class DatabaseConfig:
    """数据库配置类"""

    def __init__(self):
        self.mysql_config = self._get_mysql_config()
        self.sqlite_config = self._get_sqlite_config()

    def _get_mysql_config(self) -> Dict[str, Any]:
        """获取MySQL配置"""
        return {
            'host': os.getenv('MYSQL_HOST', 'localhost'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'user': os.getenv('MYSQL_USER', 'agent'),
            'password': os.getenv('MYSQL_PASSWORD', '123456'),
            'database': os.getenv('MYSQL_DATABASE', 'trade-ai'),
            'charset': os.getenv('MYSQL_CHARSET', 'utf8mb4')
        }

    def _get_sqlite_config(self) -> Dict[str, Any]:
        """获取SQLite配置（保留作为备用）"""
        return {
            'db_path': os.getenv('SQLITE_DB_PATH', 'data/stock_selection.db')
        }

    def get_mysql_config(self) -> Dict[str, Any]:
        """获取MySQL配置"""
        return self.mysql_config.copy()

    def get_sqlite_config(self) -> Dict[str, Any]:
        """获取SQLite配置"""
        return self.sqlite_config.copy()


# 全局配置实例
db_config = DatabaseConfig()
