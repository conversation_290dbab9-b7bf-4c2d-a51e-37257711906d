# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

import typing

from cryptography.hazmat.primitives.asymmetric import dsa

class DSAPrivateKey: ...
class DSAPublicKey: ...
class DSAParameters: ...

class DSAPrivateNumbers:
    def __init__(self, x: int, public_numbers: DSAPublicNumbers) -> None: ...
    @property
    def x(self) -> int: ...
    @property
    def public_numbers(self) -> DSAPublicNumbers: ...
    def private_key(self, backend: typing.Any = None) -> dsa.DSAPrivateKey: ...

class DSAPublicNumbers:
    def __init__(
        self, y: int, parameter_numbers: DSAParameterNumbers
    ) -> None: ...
    @property
    def y(self) -> int: ...
    @property
    def parameter_numbers(self) -> DSAParameterNumbers: ...
    def public_key(self, backend: typing.Any = None) -> dsa.DSAPublicKey: ...

class DSAParameterNumbers:
    def __init__(self, p: int, q: int, g: int) -> None: ...
    @property
    def p(self) -> int: ...
    @property
    def q(self) -> int: ...
    @property
    def g(self) -> int: ...
    def parameters(self, backend: typing.Any = None) -> dsa.DSAParameters: ...

def generate_parameters(key_size: int) -> dsa.DSAParameters: ...
